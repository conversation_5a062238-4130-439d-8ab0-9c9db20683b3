[{"id": "evt_1752860539720_41", "timestamp": "2025-07-13T00:00:00.000Z", "type": "invoice_paid", "notificationType": 41, "actorId": 32, "targetId": 31, "entityType": 5, "entityId": "MGL100000-M1", "metadata": {"invoiceNumber": "MGL100000-M1", "projectTitle": "Lagos Parks Services website re-design", "taskTitle": "Develop colour palette", "amount": 1748, "description": "Develop colour palette"}, "context": {"projectId": 301, "invoiceId": "MGL100000-M1"}}, {"id": "evt_1752860539720_43", "timestamp": "2025-07-13T00:00:00.000Z", "type": "invoice_paid", "notificationType": 41, "actorId": 33, "targetId": 31, "entityType": 5, "entityId": "CUSTOM-001-M1", "metadata": {"invoiceNumber": "CUSTOM-001-M1", "projectTitle": "Custom Brand Consultation", "taskTitle": "Brand strategy consultation", "amount": 1200, "description": "Brand strategy consultation"}, "context": {"projectId": null, "invoiceId": "CUSTOM-001-M1"}}, {"id": "evt_1752860539720_45", "timestamp": "2025-07-13T00:00:00.000Z", "type": "invoice_paid", "notificationType": 41, "actorId": 34, "targetId": 31, "entityType": 5, "entityId": "INV-303-001", "metadata": {"invoiceNumber": "MGL303001", "projectTitle": "Corlax iOS app UX", "taskTitle": "Login & onboarding flow", "amount": 2500, "description": "Login & onboarding flow"}, "context": {"projectId": 303, "invoiceId": "INV-303-001"}}, {"id": "evt_1752860539720_47", "timestamp": "2025-07-13T00:00:00.000Z", "type": "invoice_paid", "notificationType": 41, "actorId": 38, "targetId": 31, "entityType": 5, "entityId": "INV-299-001", "metadata": {"invoiceNumber": "MGL299001", "projectTitle": "UX Writing", "taskTitle": "Accessibility microcopy audit", "amount": 2500, "description": "Accessibility microcopy audit"}, "context": {"projectId": 299, "invoiceId": "INV-299-001"}}, {"id": "evt_1752860539720_49", "timestamp": "2025-07-13T00:00:00.000Z", "type": "invoice_paid", "notificationType": 41, "actorId": 35, "targetId": 31, "entityType": 5, "entityId": "INV-304-001", "metadata": {"invoiceNumber": "MGL304001", "projectTitle": "Zynate events brand toolkit", "taskTitle": "Design event templates", "amount": 2500, "description": "Design event templates"}, "context": {"projectId": 304, "invoiceId": "INV-304-001"}}, {"id": "evt_1752860539720_51", "timestamp": "2025-07-13T00:00:00.000Z", "type": "invoice_paid", "notificationType": 41, "actorId": 36, "targetId": 31, "entityType": 5, "entityId": "INV-305-001", "metadata": {"invoiceNumber": "MGL305001", "projectTitle": "HERO research launch collateral", "taskTitle": "Write product documentation", "amount": 2500, "description": "Write product documentation"}, "context": {"projectId": 305, "invoiceId": "INV-305-001"}}, {"id": "evt_1752860539720_53", "timestamp": "2025-07-13T00:00:00.000Z", "type": "invoice_paid", "notificationType": 41, "actorId": 34, "targetId": 31, "entityType": 5, "entityId": "INV-306-001", "metadata": {"invoiceNumber": "MGL306001", "projectTitle": "Nebula CMS landing redesign", "taskTitle": "CTA Button States", "amount": 2500, "description": "CTA Button States"}, "context": {"projectId": 306, "invoiceId": "INV-306-001"}}, {"id": "evt_1752860539720_55", "timestamp": "2025-07-13T00:00:00.000Z", "type": "invoice_paid", "notificationType": 41, "actorId": 32, "targetId": 31, "entityType": 5, "entityId": "INV-311-001", "metadata": {"invoiceNumber": "MGL311001", "projectTitle": "Lagos Parks Mobile App Development", "taskTitle": "User authentication flow design", "amount": 2500, "description": "User authentication flow design"}, "context": {"projectId": 311, "invoiceId": "INV-311-001"}}]