import { NextResponse } from 'next/server';
import path from 'path';
import { promises as fs } from 'fs';

export async function GET() {
  try {
    const invoiceData = await fs.readFile(
      path.join(process.cwd(), 'data/invoices.json'),
      'utf-8'
    );

    const invoices = JSON.parse(invoiceData);

    return NextResponse.json(invoices);
  } catch (err) {
    console.error('[invoices] Failed to load data:', err);
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
  }
}

export async function POST(request: Request) {
  try {
    const body = await request.json();
    const {
      invoiceNumber,
      freelancerId,
      commissionerId,
      projectId,
      projectTitle,
      issueDate,
      dueDate,
      totalAmount,
      status,
      milestones,
      isCustomProject
    } = body;

    if (!invoiceNumber || !freelancerId || !commissionerId || !projectTitle) {
      return NextResponse.json({ error: 'Missing required fields' }, { status: 400 });
    }

    const invoicesPath = path.join(process.cwd(), 'data/invoices.json');
    const invoiceData = await fs.readFile(invoicesPath, 'utf-8');
    const invoices = JSON.parse(invoiceData);

    // Check if invoice already exists
    const existingInvoice = invoices.find((inv: any) => inv.invoiceNumber === invoiceNumber);
    if (existingInvoice) {
      return NextResponse.json({ error: 'Invoice already exists' }, { status: 400 });
    }

    // Create new invoice
    const newInvoice = {
      id: invoices.length > 0 ? Math.max(...invoices.map((inv: any) => inv.id || 0)) + 1 : 1,
      invoiceNumber,
      freelancerId: Number(freelancerId),
      commissionerId: Number(commissionerId),
      projectId,
      projectTitle,
      issueDate,
      dueDate,
      totalAmount: Number(totalAmount),
      status: status || 'draft',
      milestones: milestones || [],
      isCustomProject: isCustomProject || false,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    invoices.push(newInvoice);
    await fs.writeFile(invoicesPath, JSON.stringify(invoices, null, 2));

    return NextResponse.json({
      success: true,
      message: 'Invoice created successfully',
      invoiceNumber,
      id: newInvoice.id
    });
  } catch (err) {
    console.error('[invoices] Failed to create invoice:', err);
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
  }
}
